# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

Development commands (run from project root):

```bash
bun dev          # Start dev server at localhost:4321
bun build        # Build production site to ./dist/
bun preview      # Preview build locally
bun astro ...    # Run Astro CLI commands
```

## Architecture

This is an **Astro blog** using the official blog template with these key patterns:

### Content Management
- Blog posts live in `src/content/blog/` as Markdown/MDX files
- Content schema defined in `src/content.config.ts` with Zod validation
- Frontmatter requires: `title`, `description`, `pubDate`
- Optional: `updatedDate`, `heroImage`

### Routing & Pages
- File-based routing in `src/pages/`
- Dynamic blog routes via `src/pages/blog/[...slug].astro`
- Static generation using `getStaticPaths()` and `getCollection()`

### Key Files
- `src/consts.ts` - Global site configuration (SITE_TITLE, SITE_DESCRIPTION)
- `src/layouts/BlogPost.astro` - Blog post template
- `astro.config.mjs` - Astro config with MDX and sitemap integrations

### Content Collections
Uses Astro's Content Collections API:
- `getCollection('blog')` to fetch all posts
- `render(post)` to get rendered content component
- Type-safe frontmatter via Zod schema

### Styling
- Global styles in `src/styles/global.css`
- Minimal styling approach - make it your own
- Custom fonts (Atkinson) in `public/fonts/`

## TypeScript
- Strict TypeScript config extending `astro/tsconfigs/strict`
- Content collection types auto-generated in `.astro/types.d.ts`