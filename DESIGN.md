# Design Guide

This document outlines the design system for the project, including color palettes, typography, component styling, layout, spacing, and animations.

## 1. Color Palette

Colors are defined using HSL values and CSS variables. Light and dark mode variations are provided.

**Reference Files:**
*   `tailwind.config.ts` (for color categories)
*   `src/app/globals.css` (for HSL value definitions)

**Color Naming Convention:**
*   `--{category}`: Default color for the category.
*   `--{category}-foreground`: Text/icon color to be used on top of the default category color.

---

### 1.1. Primary Colors

| Mode  | Primary                            | Primary Foreground                 |
| :---- | :--------------------------------- | :--------------------------------- |
| Light | `hsl(var(--primary))` (0 0% 9%)    | `hsl(var(--primary-foreground))` (0 0% 98%) |
| Dark  | `hsl(var(--primary))` (0 0% 98%)   | `hsl(var(--primary-foreground))` (0 0% 9%)  |

---

### 1.2. Secondary Colors

| Mode  | Secondary                            | Secondary Foreground                 |
| :---- | :----------------------------------- | :----------------------------------- |
| Light | `hsl(var(--secondary))` (0 0% 96.1%) | `hsl(var(--secondary-foreground))` (0 0% 9%) |
| Dark  | `hsl(var(--secondary))` (0 0% 14.9%) | `hsl(var(--secondary-foreground))` (0 0% 98%) |

---

### 1.3. Accent Colors

| Mode  | Accent                             | Accent Foreground                  |
| :---- | :--------------------------------- | :--------------------------------- |
| Light | `hsl(var(--accent))` (0 0% 96.1%)  | `hsl(var(--accent-foreground))` (0 0% 9%)  |
| Dark  | `hsl(var(--accent))` (0 0% 14.9%)  | `hsl(var(--accent-foreground))` (0 0% 98%)  |

---

### 1.4. Neutral & Utility Colors

These colors are used for backgrounds, text, borders, inputs, and other UI elements.

| Name                 | Light Mode Value                | Dark Mode Value                 | Notes                               |
| :------------------- | :------------------------------ | :------------------------------ | :---------------------------------- |
| `background`         | `hsl(var(--background))` (0 0% 100%) | `hsl(var(--background))` (0 0% 3.9%)  | Main page background                |
| `foreground`         | `hsl(var(--foreground))` (0 0% 3.9%)  | `hsl(var(--foreground))` (0 0% 98%)   | Main text color                     |
| `card`               | `hsl(var(--card))` (0 0% 100%)       | `hsl(var(--card))` (0 0% 3.9%)        | Card component background           |
| `card-foreground`    | `hsl(var(--card-foreground))` (0 0% 3.9%) | `hsl(var(--card-foreground))` (0 0% 98%)   | Card component text                 |
| `popover`            | `hsl(var(--popover))` (0 0% 100%)    | `hsl(var(--popover))` (0 0% 3.9%)     | Popover component background        |
| `popover-foreground` | `hsl(var(--popover-foreground))` (0 0% 3.9%) | `hsl(var(--popover-foreground))` (0 0% 98%)   | Popover component text              |
| `muted`              | `hsl(var(--muted))` (0 0% 96.1%)     | `hsl(var(--muted))` (0 0% 14.9%)      | Muted text or elements              |
| `muted-foreground`   | `hsl(var(--muted-foreground))` (0 0% 45.1%) | `hsl(var(--muted-foreground))` (0 0% 63.9%)  | Muted text foreground               |
| `border`             | `hsl(var(--border))` (0 0% 89.8%)    | `hsl(var(--border))` (0 0% 14.9%)     | Default border color                |
| `input`              | `hsl(var(--input))` (0 0% 89.8%)     | `hsl(var(--input))` (0 0% 14.9%)      | Input field border/background       |
| `ring`               | `hsl(var(--ring))` (0 0% 3.9%)       | `hsl(var(--ring))` (0 0% 83.1%)      | Focus ring color                    |

---

### 1.5. Destructive Colors

Used for actions that have destructive consequences (e.g., delete).

| Mode  | Destructive                            | Destructive Foreground                 |
| :---- | :------------------------------------- | :------------------------------------- |
| Light | `hsl(var(--destructive))` (0 84.2% 60.2%) | `hsl(var(--destructive-foreground))` (0 0% 98%) |
| Dark  | `hsl(var(--destructive))` (0 62.8% 30.6%) | `hsl(var(--destructive-foreground))` (0 0% 98%) |

---

### 1.6. Chart Colors

A set of predefined colors for charts.

| Name      | Light Mode Value             | Dark Mode Value              |
| :-------- | :--------------------------- | :--------------------------- |
| `chart-1` | `hsl(var(--chart-1))` (12 76% 61%) | `hsl(var(--chart-1))` (220 70% 50%) |
| `chart-2` | `hsl(var(--chart-2))` (173 58% 39%)| `hsl(var(--chart-2))` (160 60% 45%) |
| `chart-3` | `hsl(var(--chart-3))` (197 37% 24%)| `hsl(var(--chart-3))` (30 80% 55%)  |
| `chart-4` | `hsl(var(--chart-4))` (43 74% 66%) | `hsl(var(--chart-4))` (280 65% 60%) |
| `chart-5` | `hsl(var(--chart-5))` (27 87% 67%) | `hsl(var(--chart-5))` (340 75% 55%) |

## 2. Typography

**Reference Files:**
*   `src/app/globals.css` (for global font family)
*   Component files (e.g., `src/components/ui/button.tsx`, `src/components/ui/card.tsx`) for specific usage.

### 2.1. Global Font Family

*   **Default:** `Arial, Helvetica, sans-serif` (applied to `body`).

### 2.2. Component-Specific Typography

Tailwind utility classes are primarily used. Common examples:

| Element / Component | Tailwind Classes                                  | Font Size (approx.) | Font Weight | Line Height (approx.) | Letter Spacing |
| :------------------ | :------------------------------------------------ | :------------------ | :---------- | :-------------------- | :------------- |
| Button              | `text-sm font-medium`                             | 14px                | 500         | 20px                  | N/A            |
| Card Title          | `text-2xl font-semibold leading-none tracking-tight` | 24px                | 600         | 24px (1)              | -0.025em       |
| Card Description    | `text-sm`                                         | 14px                | Normal (400)| 20px                  | N/A            |
| Timeline Title (Desktop) | `text-2xl md:text-5xl font-bold`               | 24px / 48px         | 700         | 1 / default           | N/A            |
| Timeline Title (Mobile)  | `text-3xl font-bold`                            | 30px                | 700         | 36px                  | N/A            |
| Timeline Content    | `text-lg md:text-xl`                              | 18px / 20px         | Normal (400)| 28px                  | N/A            |

*Note: Font sizes and line heights are based on Tailwind's default scale where 1rem = 16px.*

## 3. Component Design

Styling for common UI components. Colors are sourced from the palette above.

**Reference Files:**
*   `src/components/ui/*.tsx` (especially `button.tsx`, `card.tsx`, `tooltip.tsx`)
*   `tailwind.config.ts` (for `tailwindcss-animate` plugin)

### 3.1. General Styling Notes

*   **Rounded Corners:** Based on CSS variable `--radius` (0.5rem / 8px). Utilities `rounded-md` and `rounded-lg` map to this.
*   **Focus States:** Generally use a ring: `focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2`.
*   **Disabled States:** `disabled:opacity-50 disabled:pointer-events-none`.

### 3.2. Buttons (`src/components/ui/button.tsx`)

*   **Base:** `inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors`.
*   **Variants:**
    *   `default`: `bg-primary text-primary-foreground`. Hover: `bg-primary/90`.
    *   `destructive`: `bg-destructive text-destructive-foreground`. Hover: `bg-destructive/90`.
    *   `outline`: `border border-input bg-background`. Hover: `bg-accent text-accent-foreground`.
    *   `secondary`: `bg-secondary text-secondary-foreground`. Hover: `bg-secondary/80`.
    *   `ghost`: Transparent background. Hover: `bg-accent text-accent-foreground`.
    *   `link`: `text-primary underline-offset-4`. Hover: `underline`.
*   **Sizes:**
    *   `default`: `h-10 px-4 py-2`
    *   `sm`: `h-9 rounded-md px-3`
    *   `lg`: `h-11 rounded-md px-8`
    *   `icon`: `h-10 w-10`

### 3.3. Cards (`src/components/ui/card.tsx`)

*   **Base:** `rounded-lg border bg-card text-card-foreground shadow-sm`.
    *   `rounded-lg`: Uses `var(--radius)` (0.5rem).
    *   `border`: Uses `var(--border)`.
    *   `bg-card`: Uses `var(--card)`.
    *   `text-card-foreground`: Uses `var(--card-foreground)`.
    *   `shadow-sm`: Small box shadow from Tailwind.
*   No specific hover/active states defined on the Card container itself.

### 3.4. Tooltips (`src/components/ui/tooltip.tsx`)

*   **Base (`TooltipContent`):** `z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md`.
    *   `rounded-md`: Uses `calc(var(--radius) - 2px)`.
    *   `border`: Uses `var(--border)`.
    *   `bg-popover`: Uses `var(--popover)`.
    *   `text-popover-foreground`: Uses `var(--popover-foreground)`.
    *   `shadow-md`: Medium box shadow from Tailwind.
*   **Animations:** Uses `tailwindcss-animate` for fade-in, zoom-in, slide-in effects on open, and corresponding out-animations on close.

### 3.5. Input Fields

*   No dedicated `input.tsx` component found in `src/components/ui/`.
*   Styling is likely through global styles or utility classes.
*   The CSS variable `--input` is defined:
    *   Light Mode: `hsl(0 0% 89.8%)`
    *   Dark Mode: `hsl(0 0% 14.9%)`
*   Inputs likely use a border colored by `var(--input)` and background `var(--background)`. The `outline` button variant uses `border-input`.

## 4. Layout and Spacing

**Reference Files:**
*   `tailwind.config.ts`
*   `src/app/globals.css`
*   Component files for usage examples.

### 4.1. Spacing System

*   Primarily uses **Tailwind CSS's default spacing scale**. (1 unit = 0.25rem = 4px).
    *   Common padding: `p-6` (24px), `p-4` (16px), `px-4 py-2` (16px/8px).
    *   Common margin: `mx-auto`.
    *   Common gaps: `gap-2` (8px), `space-y-1.5` (6px).
*   `--radius` CSS variable (0.5rem / 8px) for border-radius.
*   Occasional fixed pixel values (e.g., `scroll-margin-top: 120px` for sections).

### 4.2. Layout

*   **Flexbox** is the primary method for component internal layout (`flex`, `items-center`, etc.).
*   **Grid System:** Uses Tailwind's built-in grid utilities as needed.
*   **Full Width:** `w-full` is common.
*   **Max Width:** Content often constrained with `max-w-7xl` (1280px) and centered with `mx-auto`.

### 4.3. Responsive Design

*   Uses **Tailwind's default breakpoints**:
    *   `sm`: 640px
    *   `md`: 768px
    *   `lg`: 1024px
    *   `xl`: 1280px
    *   `2xl`: 1536px
*   Responsive styles applied using Tailwind's breakpoint prefixes (e.g., `md:pt-32`, `lg:max-w-sm`).
*   A custom `@media (max-width: 768px)` block in `globals.css` aligns with the `md` breakpoint for specific adjustments to `.waves-background`.

## 5. Animation and Effects

**Reference Files:**
*   `tailwind.config.ts` (for keyframes, animations, `tailwindcss-animate` plugin)
*   `src/app/globals.css` (for additional keyframes and utilities)

### 5.1. Custom Keyframes & Animations

*   **From `tailwind.config.ts` (utility classes generated):**
    *   `telegramGlow`: Pulsing `drop-shadow` (#0088cc) & scale. Animation: `animate-telegram-glow`.
    *   `pulse-glow` (config): Pulsing `boxShadow` (reddish) & scale. Animation: `animate-pulse-glow`.
    *   `float` (config): Vertical floating. Animation: `animate-float`.
    *   `spin-slow`: Slower Tailwind spin. Animation: `animate-spin-slow`.
*   **From `src/app/globals.css`:**
    *   `gradient`: Animates `background-position`. Utility: `.animate-gradient`.
    *   `wave`: Waving effect (`transform: rotate`). Utility: `.animate-wave`.
    *   `shine`: Sliding shine effect (`transform: translateX`). (Keyframes present, no direct utility class).
    *   `pulse-glow` (CSS): *Redundant with `tailwind.config.ts` version.*
    *   `float` (CSS): *Redundant with `tailwind.config.ts` version.*

    **Note on Redundancy:** The `pulse-glow` and `float` keyframes are defined in both `tailwind.config.ts` and `src/app/globals.css`. For consistency and to leverage Tailwind's utility generation, the definitions in `tailwind.config.ts` should be considered the source of truth. The CSS versions could potentially be removed after verification.

### 5.2. `tailwindcss-animate` Plugin

*   Provides utilities for common enter/exit animations (e.g., `animate-in`, `fade-in-0`, `zoom-in-95`, `slide-in-from-top-2`, and corresponding `animate-out` versions).
*   Used in `TooltipContent` for its show/hide animations.

### 5.3. Transitions

*   Standard Tailwind utility `transition-colors` used for smooth color changes (e.g., on buttons).
*   Other transitions (opacity, transform) are part of `tailwindcss-animate` utilities.

### 5.4. Visual Effects

*   **Shadows:**
    *   Tailwind: `shadow-sm` (e.g., `Card`), `shadow-md` (e.g., `TooltipContent`).
    *   Custom: `drop-shadow` in `telegramGlow`, `boxShadow` in `pulse-glow`.
*   **Glows:** Achieved via `drop-shadow` or `boxShadow` as part of animations.

---
End of Design Guide.
