---
import BaseHead from '../components/BaseHead.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import { SITE_TITLE, SITE_DESCRIPTION } from '../consts';
---

<!doctype html>
<html lang="en">
	<head>
		<BaseHead title={SITE_TITLE} description={SITE_DESCRIPTION} />
		<style>
			.hero {
				text-align: center;
				padding: 4em 2em 4em 2em;
				background: rgba(255, 255, 255, 0.9);
				border-radius: 20px;
				box-shadow: var(--box-shadow-lg);
				margin-bottom: 3em;
				border: 2px solid var(--coral-soft);
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				min-height: 300px;
				box-sizing: border-box;
			}
			.hero h1 {
				font-size: 4em;
				margin: 0 0 0.5em 0;
				color: rgb(var(--black));
				font-weight: 800;
				line-height: 1.1;
			}
			.hero p {
				font-size: 1.3em;
				color: rgb(var(--gray-dark));
				margin: 0 auto 2em auto;
				max-width: 600px;
				line-height: 1.5;
			}
			.cta-button {
				display: inline-block;
				padding: 1em 2em;
				background: var(--accent);
				color: white;
				text-decoration: none;
				border-radius: 50px;
				font-weight: 600;
				font-size: 1.1em;
				transition: all 0.3s ease;
				box-shadow: var(--box-shadow);
			}
			.cta-button:hover {
				background: var(--accent-dark);
				transform: translateY(-3px);
				box-shadow: var(--box-shadow-lg);
				border-bottom: none;
			}
			@media (max-width: 720px) {
				.hero {
					padding: 3em 1.5em 3em 1.5em;
					min-height: 250px;
				}
				.hero h1 {
					font-size: 2.5em;
					margin: 0 0 0.5em 0;
				}
				.hero p {
					font-size: 1.1em;
					margin: 0 auto 2em auto;
				}
			}
		</style>
	</head>
	<body>
		<Header />
		<main>
			<div class="hero">
				<h1>Welcome to Francesco Oddo's Blog</h1>
				<p>Exploring the world of code, technology, and continuous learning. Join me on this journey of discovery and growth.</p>
				<a href="/blog" class="cta-button">Read My Posts</a>
			</div>
		</main>
		<Footer />
	</body>
</html>
