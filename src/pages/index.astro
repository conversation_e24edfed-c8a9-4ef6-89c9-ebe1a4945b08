---
import BaseHead from '../components/BaseHead.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import { SITE_TITLE, SITE_DESCRIPTION } from '../consts';
---

<!doctype html>
<html lang="en">
	<head>
		<BaseHead title={SITE_TITLE} description={SITE_DESCRIPTION} />
		<style>
			.hero {
				text-align: center;
				padding: 4em 2em;
				background: rgba(255, 255, 255, 0.9);
				border-radius: 20px;
				box-shadow: var(--box-shadow-lg);
				margin-bottom: 3em;
				border: 2px solid var(--coral-soft);
			}
			.hero h1 {
				font-size: 4em;
				margin-bottom: 0.5em;
				color: rgb(var(--black));
				font-weight: 800;
			}
			.hero p {
				font-size: 1.3em;
				color: rgb(var(--gray-dark));
				margin-bottom: 2em;
				max-width: 600px;
				margin-left: auto;
				margin-right: auto;
			}
			.cta-button {
				display: inline-block;
				padding: 1em 2em;
				background: var(--accent);
				color: white;
				text-decoration: none;
				border-radius: 50px;
				font-weight: 600;
				font-size: 1.1em;
				transition: all 0.3s ease;
				box-shadow: var(--box-shadow);
			}
			.cta-button:hover {
				background: var(--accent-dark);
				transform: translateY(-3px);
				box-shadow: var(--box-shadow-lg);
				border-bottom: none;
			}
			@media (max-width: 720px) {
				.hero {
					padding: 3em 1.5em;
				}
				.hero h1 {
					font-size: 2.5em;
				}
				.hero p {
					font-size: 1.1em;
				}
			}
		</style>
	</head>
	<body>
		<Header />
		<main>
			<div class="hero">
				<h1>Welcome to Francesco Oddo's Blog</h1>
				<p>Exploring the world of code, technology, and continuous learning. Join me on this journey of discovery and growth.</p>
				<a href="/blog" class="cta-button">Read My Posts</a>
			</div>
		</main>
		<Footer />
	</body>
</html>
