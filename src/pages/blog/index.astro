---
import BaseHead from '../../components/BaseHead.astro';
import Header from '../../components/Header.astro';
import Footer from '../../components/Footer.astro';
import { SITE_TITLE, SITE_DESCRIPTION } from '../../consts';
import { getCollection } from 'astro:content';
import FormattedDate from '../../components/FormattedDate.astro';
import { Image } from 'astro:assets';

const posts = (await getCollection('blog')).sort(
	(a, b) => b.data.pubDate.valueOf() - a.data.pubDate.valueOf(),
);
---

<!doctype html>
<html lang="en">
	<head>
		<BaseHead title={SITE_TITLE} description={SITE_DESCRIPTION} />
		<style>
			main {
				width: 1000px;
				background: transparent;
				box-shadow: none;
				border-radius: 0;
				padding: 2em 1em;
			}
			.blog-header {
				text-align: center;
				margin-bottom: 3em;
				padding: 3em 2em;
				background: rgba(255, 255, 255, 0.9);
				border-radius: 20px;
				box-shadow: var(--box-shadow-lg);
				border: 2px solid var(--coral-soft);
			}
			.blog-header h1 {
				font-size: 3.5em;
				margin-bottom: 0.5em;
				color: rgb(var(--black));
				font-weight: 800;
			}
			.blog-header p {
				font-size: 1.2em;
				color: rgb(var(--gray-dark));
				margin: 0;
			}
			ul {
				display: grid;
				grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
				gap: 2rem;
				list-style-type: none;
				margin: 0;
				padding: 0;
			}
			ul li {
				background: rgba(255, 255, 255, 0.95);
				backdrop-filter: blur(15px);
				border-radius: 20px;
				box-shadow: var(--box-shadow);
				transition: all 0.3s ease;
				overflow: hidden;
				border: 2px solid var(--coral-soft);
			}
			ul li:hover {
				transform: translateY(-8px);
				box-shadow: var(--box-shadow-xl);
				border-color: var(--accent);
			}
			ul li:first-child {
				grid-column: 1 / -1;
				background: rgba(255, 255, 255, 0.95);
				border: 2px solid var(--sage-green);
			}
			ul li:first-child .card-content {
				padding: 3em;
				text-align: center;
			}
			ul li:first-child .title {
				font-size: 2.5em;
				color: rgb(var(--black));
				font-weight: 800;
			}
			ul li img {
				width: 100%;
				height: 200px;
				object-fit: cover;
				transition: transform 0.3s ease;
			}
			ul li:first-child img {
				height: 300px;
				border-radius: 15px;
				margin-bottom: 1em;
			}
			ul li:hover img {
				transform: scale(1.05);
			}
			ul li a {
				display: block;
				text-decoration: none;
				color: inherit;
				height: 100%;
			}
			.card-content {
				padding: 1.5em;
			}
			.title {
				margin: 0 0 0.5em 0;
				color: rgb(var(--black));
				line-height: 1.3;
				font-size: 1.4em;
				font-weight: 700;
			}
			.date {
				margin: 0;
				color: var(--accent);
				font-weight: 600;
				font-size: 0.95em;
				display: flex;
				align-items: center;
				gap: 0.5em;
			}
			.date::before {
				content: '📅';
			}
			@media (max-width: 720px) {
				main {
					padding: 1em;
				}
				ul {
					grid-template-columns: 1fr;
					gap: 1.5rem;
				}
				ul li:first-child {
					grid-column: 1;
				}
				ul li:first-child .card-content {
					padding: 2em;
				}
				ul li:first-child .title {
					font-size: 2em;
				}
				.blog-header h1 {
					font-size: 2.5em;
				}
				.blog-header {
					padding: 1.5em;
				}
			}
		</style>
	</head>
	<body>
		<Header />
		<main>
			<div class="blog-header">
				<h1>Francesco Oddo's Blog</h1>
				<p>Thoughts, learnings, and discoveries from my coding journey</p>
			</div>
			<section>
				<ul>
					{
						posts.map((post) => (
							<li>
								<a href={`/blog/${post.id}/`}>
									{post.data.heroImage && (
										<Image width={720} height={360} src={post.data.heroImage} alt="" />
									)}
									<div class="card-content">
										<h4 class="title">{post.data.title}</h4>
										<p class="date">
											<FormattedDate date={post.data.pubDate} />
										</p>
									</div>
								</a>
							</li>
						))
					}
				</ul>
			</section>
		</main>
		<Footer />
	</body>
</html>
