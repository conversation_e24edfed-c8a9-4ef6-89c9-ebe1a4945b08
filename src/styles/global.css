/*
  The CSS in this style tag is based off of Bear Blog's default CSS.
  https://github.com/HermanMartinus/bearblog/blob/297026a877bc2ab2b3bdfbd6b9f7961c350917dd/templates/styles/blog/default.css
  License MIT: https://github.com/<PERSON><PERSON><PERSON>inus/bearblog/blob/master/LICENSE.md
 */

:root {
	/* Your beautiful color palette */
	--coral-soft: #f3b7ad;
	--coral-vibrant: #ec6a52;
	--orange-warm: #f8b042;
	--sage-green: #9dbdba;
	--dusty-blue: #93aec1;

	/* Main theme colors */
	--accent: var(--coral-vibrant);
	--accent-dark: #d85a42;
	--accent-light: var(--coral-soft);
	--secondary: var(--sage-green);
	--tertiary: var(--dusty-blue);
	--highlight: var(--orange-warm);

	/* Text colors */
	--black: 45, 55, 72;
	--gray: 107, 114, 128;
	--gray-light: 243, 244, 246;
	--gray-dark: 31, 41, 55;

	/* Background gradients */
	--primary-gradient: linear-gradient(135deg, var(--coral-soft) 0%, var(--sage-green) 100%);
	--secondary-gradient: linear-gradient(135deg, var(--dusty-blue) 0%, var(--coral-soft) 100%);
	--hero-gradient: linear-gradient(135deg, rgba(243, 183, 173, 0.1) 0%, rgba(157, 189, 186, 0.1) 50%, rgba(147, 174, 193, 0.1) 100%);

	/* Enhanced shadows */
	--box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	--box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	--box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
@font-face {
	font-family: 'Atkinson';
	src: url('/fonts/atkinson-regular.woff') format('woff');
	font-weight: 400;
	font-style: normal;
	font-display: swap;
}
@font-face {
	font-family: 'Atkinson';
	src: url('/fonts/atkinson-bold.woff') format('woff');
	font-weight: 700;
	font-style: normal;
	font-display: swap;
}
body {
	font-family: 'Atkinson', sans-serif;
	margin: 0;
	padding: 0;
	text-align: left;
	background: var(--hero-gradient);
	background-attachment: fixed;
	word-wrap: break-word;
	overflow-wrap: break-word;
	color: rgb(var(--gray-dark));
	font-size: 18px;
	line-height: 1.8;
	min-height: 100vh;
}
main {
	width: 800px;
	max-width: calc(100% - 2em);
	margin: auto;
	padding: 3em 2em 4em;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10px);
	border-radius: 20px;
	box-shadow: var(--box-shadow-lg);
	margin-top: 2em;
	margin-bottom: 2em;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0 0 1rem 0;
	color: rgb(var(--black));
	line-height: 1.3;
	font-weight: 700;
}
h1 {
	font-size: 3.2em;
	color: rgb(var(--black));
	margin-bottom: 1.5rem;
	font-weight: 800;
}
h2 {
	font-size: 2.5em;
	color: var(--accent);
	margin-top: 2rem;
	margin-bottom: 1rem;
}
h3 {
	font-size: 2em;
	color: var(--secondary);
	margin-top: 1.5rem;
}
h4 {
	font-size: 1.6em;
	color: var(--tertiary);
}
h5 {
	font-size: 1.3em;
	color: var(--accent-dark);
}
strong,
b {
	font-weight: 700;
}
a {
	color: var(--accent);
}
a:hover {
	color: var(--accent);
}
p {
	margin-bottom: 1.5em;
	color: rgb(var(--gray-dark));
}
.prose p {
	margin-bottom: 2em;
	font-size: 1.1em;
	line-height: 1.8;
}

/* Enhanced link styling */
a {
	color: var(--accent);
	text-decoration: none;
	border-bottom: 2px solid transparent;
	transition: all 0.3s ease;
}
a:hover {
	color: var(--accent-dark);
	border-bottom-color: var(--accent);
}

/* List styling */
ul, ol {
	margin-bottom: 1.5em;
	padding-left: 1.5em;
}
li {
	margin-bottom: 0.5em;
	line-height: 1.7;
}
li strong {
	color: var(--accent);
}

/* Form elements */
textarea {
	width: 100%;
	font-size: 16px;
	border: 2px solid var(--sage-green);
	border-radius: 8px;
	padding: 0.5em;
}
input {
	font-size: 16px;
	border: 2px solid var(--sage-green);
	border-radius: 8px;
	padding: 0.5em;
}

/* Table styling */
table {
	width: 100%;
	border-collapse: collapse;
	margin: 1.5em 0;
}
th, td {
	padding: 0.75em;
	border-bottom: 1px solid var(--coral-soft);
}
th {
	background: var(--coral-soft);
	color: var(--gray-dark);
}

/* Image styling */
img {
	max-width: 100%;
	height: auto;
	border-radius: 12px;
	box-shadow: var(--box-shadow);
	transition: transform 0.3s ease;
}
img:hover {
	transform: scale(1.02);
}

/* Code styling */
code {
	padding: 4px 8px;
	background: var(--coral-soft);
	color: var(--gray-dark);
	border-radius: 6px;
	font-size: 0.9em;
}
pre {
	padding: 1.5em;
	border-radius: 12px;
	background: rgb(var(--gray-dark));
	color: var(--gray-light);
	overflow-x: auto;
	box-shadow: var(--box-shadow);
}
pre > code {
	all: unset;
	color: var(--gray-light);
}

/* Blockquote styling */
blockquote {
	border-left: 4px solid var(--accent);
	padding: 1em 0 1em 1.5em;
	margin: 2em 0;
	font-size: 1.2em;
	font-style: italic;
	background: rgba(243, 183, 173, 0.1);
	border-radius: 0 8px 8px 0;
}

/* HR styling */
hr {
	border: none;
	height: 2px;
	background: var(--primary-gradient);
	margin: 3em 0;
	border-radius: 1px;
}

/* Responsive design */
@media (max-width: 720px) {
	body {
		font-size: 16px;
	}
	main {
		padding: 1em;
		margin: 1em;
		border-radius: 15px;
	}
	h1 {
		font-size: 2.5em;
	}
	h2 {
		font-size: 2em;
	}
}

.sr-only {
	border: 0;
	padding: 0;
	margin: 0;
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
	/* IE6, IE7 - a 0 height clip, off to the bottom right of the visible 1px box */
	clip: rect(1px 1px 1px 1px);
	/* maybe deprecated but we need to support legacy browsers */
	clip: rect(1px, 1px, 1px, 1px);
	/* modern browsers, clip-path works inwards from each corner */
	clip-path: inset(50%);
	/* added line to stop words getting smushed together (as they go onto separate lines and some screen readers do not understand line feeds as a space */
	white-space: nowrap;
}
