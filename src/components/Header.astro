---
import HeaderLink from './HeaderLink.astro';
import { SITE_TITLE } from '../consts';
---

<header>
	<nav>
		<h2><a href="/">{SITE_TITLE}</a></h2>
		<div class="internal-links">
			<HeaderLink href="/">Home</HeaderLink>
			<HeaderLink href="/blog">Blog</HeaderLink>
			<HeaderLink href="/about">About</HeaderLink>
		</div>
		<div class="social-links">
			<a href="https://twitter.com/OxFrancesco_" target="_blank">
				<span class="sr-only">Follow <PERSON> on Twitter</span>
				<svg viewBox="0 0 16 16" aria-hidden="true" width="32" height="32" astro-icon="social/twitter"
					><path
						fill="currentColor"
						d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"
					></path></svg
				>
			</a>
			<a href="https://github.com/OxFrancesco" target="_blank">
				<span class="sr-only">Francesco Oddo's GitHub</span>
				<svg viewBox="0 0 16 16" aria-hidden="true" width="32" height="32" astro-icon="social/github"
					><path
						fill="currentColor"
						d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.012 8.012 0 0 0 16 8c0-4.42-3.58-8-8-8z"
					></path></svg
				>
			</a>
		</div>
	</nav>
</header>
<style>
	header {
		margin: 0;
		padding: 0 2em;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20px);
		box-shadow: var(--box-shadow);
		border-bottom: 3px solid var(--coral-soft);
		position: sticky;
		top: 0;
		z-index: 100;
		min-height: 80px;
		display: flex;
		align-items: center;
	}
	h2 {
		margin: 0;
		font-size: 1.5em;
		font-weight: 700;
	}

	h2 a,
	h2 a.active {
		text-decoration: none;
		color: var(--accent);
		border-bottom: none !important;
		font-weight: 800;
	}
	nav {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		max-width: 1200px;
		margin: 0 auto;
		min-height: 60px;
	}
	.internal-links {
		display: flex;
		gap: 0.8em;
		align-items: center;
	}
	nav a {
		padding: 0.75em 1.5em;
		color: rgb(var(--gray-dark));
		border-bottom: none;
		text-decoration: none;
		border-radius: 25px;
		transition: all 0.3s ease;
		font-weight: 500;
		font-size: 1rem;
		line-height: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 20px;
		box-sizing: border-box;
	}
	nav a:hover:not(.active) {
		background: rgba(243, 183, 173, 0.2);
		color: var(--accent);
		transform: translateY(-2px);
		box-shadow:
			0 6px 16px rgba(0, 0, 0, 0.1),
			0 3px 8px rgba(0, 0, 0, 0.06);
	}
	nav a.active {
		background: var(--accent);
		color: white;
		box-shadow:
			0 4px 12px rgba(236, 106, 82, 0.3),
			0 2px 6px rgba(236, 106, 82, 0.2);
	}
	.social-links {
		display: flex;
		gap: 1.5em;
		perspective: 1000px;
	}
	.social-links a {
		text-decoration: none;
		color: var(--accent);
		padding: 0.75em;
		background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(243, 183, 173, 0.3));
		border: 2px solid var(--coral-soft);
		border-radius: 12px;
		transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
		box-shadow:
			0 8px 16px rgba(0, 0, 0, 0.1),
			0 4px 8px rgba(0, 0, 0, 0.06),
			inset 0 1px 0 rgba(255, 255, 255, 0.8),
			inset 0 -1px 0 rgba(0, 0, 0, 0.1);
		position: relative;
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
		min-width: 52px;
		min-height: 52px;
		box-sizing: border-box;
	}
	.social-links a::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
		transition: left 0.5s ease;
	}
	.social-links a:hover::before {
		left: 100%;
	}
	.social-links a:hover {
		color: white;
		background: linear-gradient(145deg, var(--accent), var(--accent-dark));
		border-color: var(--accent-dark);
		transform: translateY(-6px) rotateX(15deg) rotateY(-5deg);
		box-shadow:
			0 20px 40px rgba(236, 106, 82, 0.3),
			0 10px 20px rgba(236, 106, 82, 0.2),
			inset 0 1px 0 rgba(255, 255, 255, 0.3),
			inset 0 -1px 0 rgba(0, 0, 0, 0.2);
	}
	.social-links a:active {
		transform: translateY(-2px) rotateX(5deg) rotateY(-2deg);
		box-shadow:
			0 8px 16px rgba(236, 106, 82, 0.2),
			0 4px 8px rgba(236, 106, 82, 0.1);
	}
	.social-links svg {
		width: 28px;
		height: 28px;
		transition: color 0.3s ease;
		flex-shrink: 0;
	}
	.social-links a:hover svg {
		color: white;
	}
	@media (max-width: 720px) {
		header {
			padding: 0 1em;
		}
		.social-links {
			display: none;
		}
		.internal-links {
			gap: 0.4em;
		}
		nav {
			padding: 0.8em 0;
		}
		nav a {
			padding: 0.6em 1em;
			font-size: 0.9em;
			min-height: 16px;
		}
		.social-links a {
			padding: 0.6em;
			min-width: 48px;
			min-height: 48px;
		}
		.social-links svg {
			width: 24px;
			height: 24px;
		}
		.social-links a:hover {
			transform: translateY(-4px) rotateX(10deg) rotateY(-3deg);
		}
		h2 {
			font-size: 1.2em;
		}
	}
</style>
