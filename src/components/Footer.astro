---
const today = new Date();
---

<footer>
	&copy; {today.getFullYear()} <PERSON>. All rights reserved.
	<div class="social-links">
		<a href="https://twitter.com/OxFrancesco_" target="_blank">
			<span class="sr-only">Follow <PERSON> on Twitter</span>
			<svg viewBox="0 0 16 16" aria-hidden="true" width="32" height="32" astro-icon="social/twitter"
				><path
					fill="currentColor"
					d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"
				></path></svg
			>
		</a>
	</div>
</footer>
<style>
	footer {
		padding: 3em 2em;
		background: var(--secondary-gradient);
		color: rgb(var(--gray-dark));
		text-align: center;
		margin-top: 4em;
		border-top: 3px solid var(--coral-soft);
		position: relative;
		overflow: hidden;
	}
	footer::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(10px);
		z-index: -1;
	}
	.social-links {
		display: flex;
		justify-content: center;
		gap: 1.5em;
		margin-top: 1.5em;
		perspective: 1000px;
	}
	.social-links a {
		text-decoration: none;
		color: var(--accent);
		padding: 1.2em 1.2em;
		background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(243, 183, 173, 0.3));
		border: 2px solid var(--coral-soft);
		border-radius: 12px;
		transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
		box-shadow:
			0 8px 16px rgba(0, 0, 0, 0.1),
			0 4px 8px rgba(0, 0, 0, 0.06),
			inset 0 1px 0 rgba(255, 255, 255, 0.8),
			inset 0 -1px 0 rgba(0, 0, 0, 0.1);
		position: relative;
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 48px;
		height: 48px;
		box-sizing: border-box;
	}
	.social-links a::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
		transition: left 0.5s ease;
	}
	.social-links a:hover::before {
		left: 100%;
	}
	.social-links a:hover {
		color: white;
		background: linear-gradient(145deg, var(--accent), var(--accent-dark));
		border-color: var(--accent-dark);
		transform: translateY(-6px) rotateX(15deg) rotateY(-5deg);
		box-shadow:
			0 20px 40px rgba(236, 106, 82, 0.3),
			0 10px 20px rgba(236, 106, 82, 0.2),
			inset 0 1px 0 rgba(255, 255, 255, 0.3),
			inset 0 -1px 0 rgba(0, 0, 0, 0.2);
	}
	.social-links a:active {
		transform: translateY(-2px) rotateX(5deg) rotateY(-2deg);
		box-shadow:
			0 8px 16px rgba(236, 106, 82, 0.2),
			0 4px 8px rgba(236, 106, 82, 0.1);
	}
	.social-links svg {
		width: 24px;
		height: 24px;
		transition: color 0.3s ease;
	}
	.social-links a:hover svg {
		color: white;
	}
	footer p {
		margin: 0;
		font-size: 1.1em;
		font-weight: 500;
	}
	@media (max-width: 720px) {
		footer {
			padding: 2em 1em;
		}
		.social-links {
			gap: 1em;
		}
		.social-links a {
			padding: 1em 1em;
			width: 44px;
			height: 44px;
		}
		.social-links svg {
			width: 20px;
			height: 20px;
		}
		.social-links a:hover {
			transform: translateY(-4px) rotateX(10deg) rotateY(-3deg);
		}
	}
</style>
