---
const today = new Date();
---

<footer>
	&copy; {today.getFullYear()} <PERSON>. All rights reserved.
	<div class="social-links">
		<a href="https://twitter.com/OxFrancesco_" target="_blank">
			<span class="sr-only">Follow <PERSON> on Twitter</span>
			<svg viewBox="0 0 16 16" aria-hidden="true" width="32" height="32" astro-icon="social/twitter"
				><path
					fill="currentColor"
					d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"
				></path></svg
			>
		</a>
	</div>
</footer>
<style>
	footer {
		padding: 3em 2em;
		background: var(--secondary-gradient);
		color: rgb(var(--gray-dark));
		text-align: center;
		margin-top: 4em;
		border-top: 3px solid var(--coral-soft);
		position: relative;
		overflow: hidden;
	}
	footer::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(10px);
		z-index: -1;
	}
	.social-links {
		display: flex;
		justify-content: center;
		gap: 1.5em;
		margin-top: 1.5em;
	}
	.social-links a {
		text-decoration: none;
		color: var(--accent);
		padding: 1em;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.8);
		transition: all 0.3s ease;
		box-shadow: var(--box-shadow);
	}
	.social-links a:hover {
		color: white;
		background: var(--accent);
		transform: translateY(-3px) scale(1.1);
		box-shadow: var(--box-shadow-lg);
	}
	.social-links svg {
		width: 28px;
		height: 28px;
	}
	footer p {
		margin: 0;
		font-size: 1.1em;
		font-weight: 500;
	}
	@media (max-width: 720px) {
		footer {
			padding: 2em 1em;
		}
		.social-links {
			gap: 1em;
		}
		.social-links a {
			padding: 0.8em;
		}
		.social-links svg {
			width: 24px;
			height: 24px;
		}
	}
</style>
