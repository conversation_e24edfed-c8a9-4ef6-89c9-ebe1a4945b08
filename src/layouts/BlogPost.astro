---
import type { CollectionEntry } from 'astro:content';
import BaseHead from '../components/BaseHead.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import FormattedDate from '../components/FormattedDate.astro';
import { Image } from 'astro:assets';

type Props = CollectionEntry<'blog'>['data'];

const { title, description, pubDate, updatedDate, heroImage } = Astro.props;
---

<html lang="en">
	<head>
		<BaseHead title={title} description={description} />
		<style>
			main {
				width: calc(100% - 2em);
				max-width: 100%;
				margin: 0;
				background: transparent;
				box-shadow: none;
				border-radius: 0;
				padding: 0;
			}
			.hero-image {
				width: 100%;
				margin-bottom: 2em;
			}
			.hero-image img {
				display: block;
				margin: 0 auto;
				border-radius: 20px;
				box-shadow: var(--box-shadow-xl);
				transition: transform 0.3s ease;
			}
			.hero-image img:hover {
				transform: scale(1.02);
			}
			.prose {
				width: 800px;
				max-width: calc(100% - 2em);
				margin: auto;
				padding: 2em;
				color: rgb(var(--gray-dark));
				background: rgba(255, 255, 255, 0.9);
				backdrop-filter: blur(15px);
				border-radius: 20px;
				box-shadow: var(--box-shadow-lg);
			}
			.title {
				margin-bottom: 2em;
				padding: 2em 0;
				text-align: center;
				line-height: 1.2;
				background: var(--hero-gradient);
				border-radius: 15px;
				margin: -2em -2em 2em -2em;
				padding: 3em 2em;
			}
			.title h1 {
				margin: 0 0 1em 0;
				font-size: 3.5em;
				background: var(--primary-gradient);
				background-clip: text;
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}
			.date {
				margin-bottom: 1em;
				color: var(--accent);
				font-size: 1.1em;
				font-weight: 600;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 0.5em;
			}
			.date::before {
				content: '📅';
				font-size: 1.2em;
			}
			.last-updated-on {
				font-style: italic;
				color: var(--tertiary);
				margin-top: 0.5em;
				font-size: 0.95em;
			}
			.last-updated-on::before {
				content: '🔄';
				margin-right: 0.5em;
			}
			hr {
				border: none;
				height: 3px;
				background: var(--primary-gradient);
				margin: 2em 0;
				border-radius: 2px;
			}
			@media (max-width: 720px) {
				.prose {
					padding: 1.5em;
					border-radius: 15px;
				}
				.title {
					margin: -1.5em -1.5em 1.5em -1.5em;
					padding: 2em 1.5em;
				}
				.title h1 {
					font-size: 2.5em;
				}
			}
		</style>
	</head>

	<body>
		<Header />
		<main>
			<article>
				<div class="hero-image">
					{heroImage && <Image width={1020} height={510} src={heroImage} alt="" />}
				</div>
				<div class="prose">
					<div class="title">
						<div class="date">
							<FormattedDate date={pubDate} />
							{
								updatedDate && (
									<div class="last-updated-on">
										Last updated on <FormattedDate date={updatedDate} />
									</div>
								)
							}
						</div>
						<h1>{title}</h1>
						<hr />
					</div>
					<slot />
				</div>
			</article>
		</main>
		<Footer />
	</body>
</html>
