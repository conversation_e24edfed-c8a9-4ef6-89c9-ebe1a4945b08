---
title: 'What I learned this week!'
description: 'Everything I learned this week!'
pubDate: 2025-13-06
---

# What I learned this week!

<PERSON> Code is a fucking beast, is not just the best Agentic Coders, Is also one of the best agents. Period.

So now my workflow is: <PERSON> (Sonnet 4), <PERSON><PERSON>ent Code, <PERSON>ursor (o3), <PERSON> Rabbit.

Other cool shit:

*   **CLI Power-Ups:**
    *   **The Fuck:** Instantly corrects your last mistyped terminal command.
    *   **Eza:** A modern, colorful, and feature-rich replacement for the `ls` command.
    *   **Fzf:** A lightning-fast fuzzy finder for files, command history, and more.
    *   **`cursor .`:** A quick command to open the current folder or a file in your editor.

*   **Global Claude Prompts:** Store reusable prompts in `~/.claude/commands` for global access in [Claude Code](https://chat4data.ai/), streamlining your AI-driven development.

*   **Web3 Wallet Trends:** [Dune's "Wallet Report v2"](https://dune.com/blog/wallet-report-v2) shows wallets are becoming more complex and modular, moving towards customizable, user-friendly experiences.

*   **Ultra-Fast Vite Builds:** Vite is developing **Rolldown**, a new Rust-based bundler set to replace esbuild and Rollup for massive performance gains. [Read the announcement](https://voidzero.dev/posts/announcing-rolldown-vite).

*   **React Performance Check:** Use [React Scan](https://react-scan.com/) to automatically find and fix performance issues in your React apps by detecting unnecessary re-renders.

*   **Docker MCP Toolkit:** A new beta feature in Docker Desktop that lets you connect different machines and host containerized AI tools together. Check your Docker Desktop app for updates.