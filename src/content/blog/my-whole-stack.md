---
title: 'My Whole Life Stack'
description: 'A deep dive into my complete technology stack and development setup.'
pubDate: 2025-08-19
---

So this will be divided in: Desktop Apps, Mobile Apps, WebApps, CLIs, VSCode Extentions, Accessories.

### DESKTOP APPS & EXTENTIONS
First of all, right now I own a Macbook Pro M1 Pro with 16GB.

**My Favourite APPs are:**
1. Notion Calendar
2. Cursor (My IDE)
3. <PERSON> from Perplexity (My Broswer)
4. <PERSON><PERSON> (My main chat client) where I have my DMs for:
    - Whatsapp
    - Telegram
    - Discord
    - Twitter
    - Slack
5. Orbstack (My Docker Client)
6. Docker (For running the MCP Server)
7. <PERSON>y (My Terminal)
8. Spark (My E-mail Client)
9. Bit<PERSON><PERSON> (My Password Manager)
10. OBS (For Streaming and Screen Recording)

**My Favourite Extentions are:**
1. Raycast (Spotlight Alternative) that is so good that I will make a whole video about it soon.
2. Cleanshot (To make Screenshot and Quick Videos)
3. Superwhisper (Speach to text) 

### MOBILE APPS by USAGE
1. YouTube
2. Beeper 
3. Spotify
4. Twitter
5. Solo Leveling Arise
6. Brave (Mobile Browser)
7. Base (Coinbase Wallet)
8. Perplexity
9. T3 Chat Shortcut
10. Spark (My E-mail Client)
11. Raindrop (My Bookmark Manager)
12. Authy (My 2FA App)

### WEB APPS
1. Linear (Task Manager)
2. Notion (Notes and Knowledge Base)
3. ChatGPT Pro
4. Jules (Async Coding Agent from Google)
5. Codex (Async Coding Agent from Openai)
6. Github
7. T3 Chat (My Main Chatbot)
8. Exalidraw (For Diagrams, Flowcharts, Brainstorms)

### CODING STACK
1. Vercel (Main Hosting Platform)
2. Convex (Main BaaS)
3. Supabase (Postgress DB)
4. Cler (For Auth)
5. NextJS (Main Framework)
6. AISDK from Vercel (Main Agent Framework)
7. TS (Main Main Language)
8. React (Main UI Framework)
9. Astro (Blog Framework)
10. Tailwind (Main CSS Framework)
11. OpenRouter (Main LLM Provider)
12. Chef (Main Vibecode Website for Backend Heavy Apps)
13. V0 (For Frontend Heavy Apps)
14. Bun (Main Package Manager)

### AI STACK
General Tasks Use (Based on Complexity)

Easy: GPT5 or GPT5-Mini or Gemini 2.5 Flash w/ search
Medium: GPT5 with Thinking or Grok4 or Gemini 2.5 Pro (If you need great multimodality, yt supported)
Hard: GPT5-PRO, Gemini 2.5 DeepThink, Grok 4 Heavy

Researches: Gemini Deep Research / OpenAI DeepResearch or Agent

Agentic Coding

1. Opus 4.1 (Much more costly then Sonnet 4)
2. Sonnet 4 (Almost on par with Opus)
3. GPT5 High Reasoning (Amazing for PRD, BUG Analysis and Fix), not ready yet for full Agentic Coding because right now Claude Code is Pareto Superior
4. Qwen3 Coder (Fully OSS and really good, great at tool calling, pretty cheap).

Agentic Use (For Agentic Apps)

1. GPT5 & Sonnet 4
2. GPT5 Mini
3. Kimi K2 from Moonshot

IMAGE GENERATION

1. GPT-1 (Images, great for both full generations and edits)
2. Imagen 4 Ultra
3. Ideogram

VIDEO GENERATION
1. Veo 3 Quality (https://x.com/lmarena_ai/status/1953126364502212659?t=MmNwP_2cQOIpxuUtLLHm4w&s=19) (Also has character consistency)
2. Veo 3 Fast